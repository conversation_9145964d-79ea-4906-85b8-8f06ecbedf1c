// Product Selection Components Export
export { ProductTypeSelection } from './ProductTypeSelection';
export { FabricSelection } from './FabricSelection';
export { ColorSelection } from './ColorSelection';
export { SizeSelection } from './SizeSelection';
export { StickyPriceBar } from './StickyPriceBar';
export { ProductSelectionStepper } from './ProductSelectionStepper';

// Re-export types for convenience
export type {
  ProductSelectionFlow,
  ProductSelectionStep,
  ProductSelections,
  SelectionPricing,
  SelectedProductType,
  SelectedFabric,
  SelectedColor,
  SelectedSize,
  EmotionalMoment,
  PersonalityInsights,
  StepperState,
  StepDefinition,
  PriceBarState
} from '@/types/product-selection';
