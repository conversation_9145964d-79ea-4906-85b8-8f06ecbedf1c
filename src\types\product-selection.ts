
// Product Selection Flow - Emotional Design Types
// Focus on identity expression, self-creation, and pride in ownership

export interface ProductSelectionFlow {
  id: string;
  userId?: string;
  sessionId: string;
  
  // Current state
  currentStep: ProductSelectionStep;
  selections: ProductSelections;
  
  // Emotional journey tracking
  emotionalJourney: EmotionalMoment[];
  personalityProfile?: PersonalityInsights;
  
  // Pricing & value
  pricing: SelectionPricing;
}

export type ProductSelectionStep = 'product-type' | 'fabric' | 'color' | 'size' | 'review';

export interface ProductSelections {
  productType?: SelectedProductType;
  fabric?: SelectedFabric;
  color?: SelectedColor;
  size?: SelectedSize;
  quantity: number;
}

export interface SelectedProductType {
  id: string;
  name: string;
  emotionalReason: string; // Why they chose this - captured from UI
  lifestyleMatch: string[]; // Which lifestyle tags resonated
  confidenceLevel: number; // 1-5 how confident they felt about choice
}

export interface SelectedFabric {
  id: string;
  name: string;
  sensoryAppeal: string; // What attracted them - "softness", "breathability"
  comfortPriority: string; // "all-day wear", "special occasions"
  touchExperience: number; // 1-5 rating of tactile appeal
}

export interface SelectedColor {
  id: string;
  name: string;
  hexCode: string;
  emotionalConnection: string; // "makes me feel powerful"
  personalityExpression: string; // "bold", "sophisticated", "playful"
  occasionIntent: string[]; // Where they plan to wear it
}

export interface SelectedSize {
  id: string;
  name: string;
  fitPreference: string; // "relaxed", "fitted", "oversized"
  confidenceBoost: string; // How this size makes them feel
  bodyPositivity: number; // 1-5 confidence in fit
}

export interface SelectionPricing {
  basePrice: number;
  totalPrice: number;
  currency: string;
  breakdown: PriceComponent[];
  valueMessage: string;
  savingsMessage?: string;
  qualityPromise: string;
}

export interface PriceComponent {
  id: string;
  name: string;
  amount: number;
  type: 'base' | 'upgrade' | 'discount';
  emotionalValue: string; // "Premium comfort upgrade"
  icon: string;
}

// Emotional journey tracking
export interface EmotionalMoment {
  step: ProductSelectionStep;
  timestamp: Date;
  emotion: EmotionalState;
  trigger: string; // What caused this emotion
  confidence: number; // 1-5
  excitement: number; // 1-5
  satisfaction: number; // 1-5
}

export type EmotionalState = 
  | 'excited' 
  | 'confident' 
  | 'inspired' 
  | 'uncertain' 
  | 'overwhelmed' 
  | 'satisfied' 
  | 'proud';

export interface PersonalityInsights {
  stylePersonality: StylePersonality;
  colorPreferences: ColorPsychology;
  lifestyleAlignment: LifestyleMatch;
  confidenceDrivers: string[];
}

export interface StylePersonality {
  primary: string; // "Bold Trendsetter", "Classic Minimalist"
  traits: string[];
  description: string;
  styleIcons: string[];
}

export interface ColorPsychology {
  dominantMoods: string[];
  avoidedColors: string[];
  seasonalPreference: string;
  emotionalDrivers: string[];
}

export interface LifestyleMatch {
  primaryContexts: string[]; // "professional", "creative", "active"
  aspirationalMoments: string[]; // Where they want to feel confident
  valueAlignment: string[]; // "sustainability", "quality", "uniqueness"
}

// UI Animation & Interaction Types
export interface SelectionAnimation {
  type: 'fadeIn' | 'slideUp' | 'parallax' | 'scale' | 'glow';
  duration: number;
  delay?: number;
  easing: string;
  trigger: 'onMount' | 'onHover' | 'onSelect' | 'onComplete';
}

export interface TouchInteraction {
  type: 'tap' | 'swipe' | 'pinch' | 'longPress';
  feedback: 'haptic' | 'visual' | 'audio';
  response: string; // What happens
}

// Stepper & Navigation
export interface StepperState {
  steps: StepDefinition[];
  currentStepIndex: number;
  canGoBack: boolean;
  canGoForward: boolean;
  progress: number; // 0-100
}

export interface StepDefinition {
  id: ProductSelectionStep;
  title: string;
  subtitle: string;
  icon: string;
  isComplete: boolean;
  isActive: boolean;
  emotionalPrompt: string; // "What makes you feel unstoppable?"
}

// Sticky Price Bar
export interface PriceBarState {
  isVisible: boolean;
  isExpanded: boolean;
  showBreakdown: boolean;
  animationState: 'idle' | 'updating' | 'celebrating';
  ctaText: string; // "Make it yours", "Almost there", "Complete your vision"
  valueHighlight?: string; // Special messaging
}

// Mobile-first responsive breakpoints
export interface ResponsiveBreakpoints {
  mobile: number; // 0-640px
  tablet: number; // 641-1024px
  desktop: number; // 1025px+
}

// Accessibility & Inclusive Design
export interface AccessibilityFeatures {
  highContrast: boolean;
  largeText: boolean;
  reducedMotion: boolean;
  screenReader: boolean;
  keyboardNavigation: boolean;
}

// Error states & validation
export interface SelectionError {
  step: ProductSelectionStep;
  field?: string;
  message: string;
  emotionalSupport: string; // Encouraging message
  suggestion: string; // How to fix
}

// Success & completion states
export interface CompletionCelebration {
  message: string;
  animation: SelectionAnimation;
  sharePrompt: string;
  nextSteps: string[];
  prideMoment: string; // "You've created something uniquely yours!"
}

